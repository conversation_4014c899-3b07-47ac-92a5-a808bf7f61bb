﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\VolvoFlashWR.Core\VolvoFlashWR.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="System.IO.Ports" Version="9.0.4" />
    <PackageReference Include="System.Management" Version="9.0.5" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>

  <PropertyGroup Condition="'$(OS)' == 'Windows_NT'">
    <DefineConstants>WINDOWS</DefineConstants>
  </PropertyGroup>

</Project>
