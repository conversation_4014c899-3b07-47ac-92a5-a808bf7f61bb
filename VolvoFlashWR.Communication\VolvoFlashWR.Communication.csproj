<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\VolvoFlashWR.Core\VolvoFlashWR.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- Core Communication Packages -->
    <PackageReference Include="System.IO.Ports" Version="9.0.4" />
    <PackageReference Include="System.Management" Version="9.0.5" />

    <!-- USB Communication for Vocom Hardware -->
    <PackageReference Include="LibUsbDotNet" Version="3.0.167-alpha" />
    <PackageReference Include="HidSharp" Version="2.1.0" />

    <!-- Serial and Device Communication -->
    <PackageReference Include="System.Device.Gpio" Version="3.2.0" />
    <PackageReference Include="System.IO.Pipelines" Version="8.0.0" />

    <!-- Network Communication (WiFi/Bluetooth) -->
    <PackageReference Include="System.Net.NetworkInformation" Version="4.3.0" />
    <PackageReference Include="InTheHand.Net.Bluetooth" Version="4.1.40" />

    <!-- Modern P/Invoke and Interop -->
    <PackageReference Include="Microsoft.Win32.Registry" Version="5.0.0" />
    <PackageReference Include="System.Runtime.InteropServices" Version="4.3.0" />

    <!-- JSON and Configuration -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />

    <!-- Logging and Diagnostics -->
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />

    <!-- Memory and Performance -->
    <PackageReference Include="System.Memory" Version="4.5.5" />
    <PackageReference Include="System.Buffers" Version="4.5.1" />

    <!-- Compression and Archives -->
    <PackageReference Include="System.IO.Compression" Version="4.3.0" />
    <PackageReference Include="SharpCompress" Version="0.37.2" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <PlatformTarget>x64</PlatformTarget>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <SelfContained>false</SelfContained>
  </PropertyGroup>

  <PropertyGroup Condition="'$(OS)' == 'Windows_NT'">
    <DefineConstants>WINDOWS</DefineConstants>
  </PropertyGroup>

</Project>
