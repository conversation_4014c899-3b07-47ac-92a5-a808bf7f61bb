using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using LibUsbDotNet;
using LibUsbDotNet.Main;
using HidSharp;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Modern USB communication service for Vocom adapters using LibUsbDotNet and HidSharp
    /// Replaces legacy Phoenix APCI libraries with modern .NET 8.0 compatible packages
    /// </summary>
    public class ModernUSBCommunicationService : IUSBCommunicationService
    {
        private readonly ILoggingService _logger;
        private bool _isConnected;
        private UsbDevice? _usbDevice;
        private UsbEndpointWriter? _writer;
        private UsbEndpointReader? _reader;
        private HidDevice? _hidDevice;
        private HidStream? _hidStream;

        // Vocom adapter USB identifiers (common values)
        private const int VOCOM_VENDOR_ID = 0x0BDA;  // Realtek (common for Vocom adapters)
        private const int VOCOM_PRODUCT_ID = 0x8150; // Common Vocom product ID
        
        // Alternative identifiers to try
        private static readonly (int VendorId, int ProductId)[] VocomIdentifiers = new[]
        {
            (0x0BDA, 0x8150), // Realtek based
            (0x1A12, 0x0001), // CSR based
            (0x04B4, 0x1004), // Cypress based
            (0x0403, 0x6001), // FTDI based
        };

        public ModernUSBCommunicationService(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public bool IsConnected => _isConnected;

        public async Task<bool> ConnectAsync(VocomConnectionSettings settings)
        {
            try
            {
                _logger.LogInformation("Attempting modern USB connection to Vocom adapter", "ModernUSBCommunicationService");
                
                // Try LibUsbDotNet first
                if (await TryConnectWithLibUsbAsync())
                {
                    _isConnected = true;
                    _logger.LogInformation("USB connection established successfully using LibUsbDotNet", "ModernUSBCommunicationService");
                    return true;
                }
                
                // Try HidSharp as fallback
                if (await TryConnectWithHidSharpAsync())
                {
                    _isConnected = true;
                    _logger.LogInformation("USB connection established successfully using HidSharp", "ModernUSBCommunicationService");
                    return true;
                }
                
                _logger.LogWarning("No Vocom adapter found via USB", "ModernUSBCommunicationService");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to establish USB connection", "ModernUSBCommunicationService", ex);
                return false;
            }
        }

        private async Task<bool> TryConnectWithLibUsbAsync()
        {
            try
            {
                _logger.LogInformation("Searching for Vocom adapter using LibUsbDotNet", "ModernUSBCommunicationService");
                
                // Try each known Vocom identifier
                foreach (var (vendorId, productId) in VocomIdentifiers)
                {
                    UsbDeviceFinder finder = new UsbDeviceFinder(vendorId, productId);
                    _usbDevice = UsbDevice.OpenUsbDevice(finder);
                    
                    if (_usbDevice != null)
                    {
                        _logger.LogInformation($"Found Vocom device with VID:PID {vendorId:X4}:{productId:X4}", "ModernUSBCommunicationService");
                        break;
                    }
                }
                
                if (_usbDevice == null)
                {
                    _logger.LogInformation("Vocom adapter not found with LibUsbDotNet", "ModernUSBCommunicationService");
                    return false;
                }
                
                // Configure the device
                if (_usbDevice is IUsbDevice wholeUsbDevice)
                {
                    wholeUsbDevice.SetConfiguration(1);
                    wholeUsbDevice.ClaimInterface(0);
                }
                
                // Get endpoints
                _writer = _usbDevice.OpenEndpointWriter(WriteEndpointID.Ep01);
                _reader = _usbDevice.OpenEndpointReader(ReadEndpointID.Ep01);
                
                _logger.LogInformation($"Connected to Vocom adapter: {_usbDevice.Info.ProductString}", "ModernUSBCommunicationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"LibUsbDotNet connection failed: {ex.Message}", "ModernUSBCommunicationService");
                return false;
            }
        }

        private async Task<bool> TryConnectWithHidSharpAsync()
        {
            try
            {
                _logger.LogInformation("Searching for Vocom adapter using HidSharp", "ModernUSBCommunicationService");
                
                // Find HID devices that might be Vocom adapters
                var devices = DeviceList.Local.GetHidDevices();
                
                foreach (var device in devices)
                {
                    // Check against known Vocom identifiers
                    foreach (var (vendorId, productId) in VocomIdentifiers)
                    {
                        if (device.VendorID == vendorId && device.ProductID == productId)
                        {
                            try
                            {
                                _hidDevice = device;
                                _hidStream = device.Open();
                                
                                _logger.LogInformation($"Connected to Vocom HID device: {device.GetProductName()}", "ModernUSBCommunicationService");
                                return true;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning($"Failed to open HID device: {ex.Message}", "ModernUSBCommunicationService");
                            }
                        }
                    }
                }
                
                _logger.LogInformation("No compatible Vocom HID device found", "ModernUSBCommunicationService");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"HidSharp connection failed: {ex.Message}", "ModernUSBCommunicationService");
                return false;
            }
        }

        public async Task DisconnectAsync()
        {
            try
            {
                if (_isConnected)
                {
                    _logger.LogInformation("Disconnecting USB connection", "ModernUSBCommunicationService");
                    
                    // Close LibUsbDotNet resources
                    if (_usbDevice != null)
                    {
                        if (_usbDevice is IUsbDevice wholeUsbDevice)
                        {
                            wholeUsbDevice.ReleaseInterface(0);
                        }
                        _usbDevice.Close();
                        _usbDevice = null;
                    }
                    
                    // Close HidSharp resources
                    _hidStream?.Close();
                    _hidStream = null;
                    _hidDevice = null;
                    
                    _writer = null;
                    _reader = null;
                    
                    _isConnected = false;
                    _logger.LogInformation("USB connection disconnected successfully", "ModernUSBCommunicationService");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error during USB disconnection", "ModernUSBCommunicationService", ex);
            }
        }

        public async Task<byte[]> SendCommandAsync(byte[] command)
        {
            if (!_isConnected)
            {
                throw new InvalidOperationException("USB connection is not established");
            }

            try
            {
                _logger.LogInformation($"Sending USB command: {BitConverter.ToString(command)}", "ModernUSBCommunicationService");
                
                byte[] response = Array.Empty<byte>();
                
                // Try LibUsbDotNet first
                if (_writer != null && _reader != null)
                {
                    response = await SendCommandWithLibUsbAsync(command);
                }
                // Try HidSharp as fallback
                else if (_hidStream != null)
                {
                    response = await SendCommandWithHidSharpAsync(command);
                }
                else
                {
                    throw new InvalidOperationException("No valid USB connection available");
                }
                
                _logger.LogInformation($"Received USB response: {BitConverter.ToString(response)}", "ModernUSBCommunicationService");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error sending USB command", "ModernUSBCommunicationService", ex);
                throw;
            }
        }

        private async Task<byte[]> SendCommandWithLibUsbAsync(byte[] command)
        {
            if (_writer == null || _reader == null)
                throw new InvalidOperationException("LibUsb endpoints not available");

            // Send command
            int bytesWritten;
            var writeResult = _writer.Write(command, 5000, out bytesWritten);
            
            if (writeResult != ErrorCode.None)
            {
                throw new InvalidOperationException($"USB write failed: {writeResult}");
            }

            // Read response
            byte[] buffer = new byte[1024];
            int bytesRead;
            var readResult = _reader.Read(buffer, 5000, out bytesRead);
            
            if (readResult != ErrorCode.None)
            {
                throw new InvalidOperationException($"USB read failed: {readResult}");
            }

            // Return only the actual data
            byte[] response = new byte[bytesRead];
            Array.Copy(buffer, response, bytesRead);
            return response;
        }

        private async Task<byte[]> SendCommandWithHidSharpAsync(byte[] command)
        {
            if (_hidStream == null)
                throw new InvalidOperationException("HID stream not available");

            // Send command
            await _hidStream.WriteAsync(command, 0, command.Length);

            // Read response
            byte[] buffer = new byte[1024];
            int bytesRead = await _hidStream.ReadAsync(buffer, 0, buffer.Length);

            // Return only the actual data
            byte[] response = new byte[bytesRead];
            Array.Copy(buffer, response, bytesRead);
            return response;
        }

        public void Dispose()
        {
            DisconnectAsync().Wait();
            
            // Clean up LibUsbDotNet resources
            UsbDevice.Exit();
        }

        // Additional methods required by IUSBCommunicationService interface
        public async Task<bool> InitializeAsync()
        {
            _logger.LogInformation("Initializing modern USB communication service", "ModernUSBCommunicationService");
            return true;
        }

        public Task<bool> IsUSBAvailableAsync()
        {
            try
            {
                // Check if any USB devices are available
                var devices = DeviceList.Local.GetHidDevices();
                bool available = devices.Any();
                
                _logger.LogInformation($"USB availability check: {available}", "ModernUSBCommunicationService");
                return Task.FromResult(available);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error checking USB availability", "ModernUSBCommunicationService", ex);
                return Task.FromResult(false);
            }
        }
    }
}
