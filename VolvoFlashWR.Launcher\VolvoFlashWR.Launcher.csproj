<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <StartupObject>VolvoFlashWR.Launcher.STAProgram</StartupObject>
    <ApplicationHighDpiMode>PerMonitorV2</ApplicationHighDpiMode>
    <ApplicationUseCompatibleTextRenderingDefault>true</ApplicationUseCompatibleTextRenderingDefault>
    <ApplicationDefaultFont>Segoe UI, 9pt</ApplicationDefaultFont>
    <EnableDefaultApplicationDefinition>false</EnableDefaultApplicationDefinition>
    <!-- <ApplicationIcon>VolvoFlashWR.ico</ApplicationIcon> -->
    <Version>1.0.0</Version>
    <Authors>S.A.H Software Solutions</Authors>
    <Company>S.A.H Software Solutions</Company>
    <Product>Volvo Flash WR</Product>
    <Description>ECU Management Tool for Volvo vehicles</Description>
    <Copyright>© 2025 All Rights Reserved For S.A.H Software Solutions Company</Copyright>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>false</SelfContained>
    <PlatformTarget>x64</PlatformTarget>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>false</PublishReadyToRun>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\VolvoFlashWR.UI\VolvoFlashWR.UI.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- Modern USB/Serial Communication -->
    <PackageReference Include="LibUsbDotNet" Version="3.0.102" />
    <PackageReference Include="HidSharp" Version="2.1.0" />
    <PackageReference Include="System.IO.Ports" Version="9.0.4" />

    <!-- Configuration and Logging -->
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />

    <!-- Dependency Injection -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />

    <!-- JSON Processing -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <!-- Copy essential configuration files only -->
  <ItemGroup>
    <!-- Configuration Files -->
    <None Include="..\Drivers\MC9S12XEP100\config.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>Drivers\MC9S12XEP100\config.json</TargetPath>
    </None>
    <None Include="..\Drivers\Vocom\config.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>Drivers\Vocom\config.json</TargetPath>
    </None>
  </ItemGroup>

  <!-- <ItemGroup>
    <None Remove="VolvoFlashWR.ico" />
    <Content Include="VolvoFlashWR.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup> -->

</Project>
